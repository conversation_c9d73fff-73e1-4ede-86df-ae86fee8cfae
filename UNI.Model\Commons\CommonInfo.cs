using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UNI.Model.Commons
{
    public class CommonInfo : BaseCtrlClient
    {
        public string ConnectionString { get; set; }
        public DatabaseType DatabaseType { get; set; } = DatabaseType.SqlServer;
        public bool IsAcceptLanguage { get; set; }
        public string AcceptLanguage { get; set; }
        public ClientTypes ClientType { get; set; }
        public string CurrUserId { get => UserId; }
        public string CommonFilterStored { get; set; }
    }

    public enum ClientTypes
    {
        None,
        Mobile,
        Web
    }
}